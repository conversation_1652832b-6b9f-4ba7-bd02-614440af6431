/* Reset ve Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* <PERSON> */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    
    /* <PERSON><PERSON> */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Di<PERSON><PERSON>kler */
    --white: #ffffff;
    --black: #000000;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--white);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-center { text-align: center; }

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-large {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-base);
}

.btn-full {
    width: 100%;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--gray-200);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.navbar {
    padding: var(--spacing-4) 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand .logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: var(--spacing-8);
    margin: 0;
}

.nav-link {
    color: var(--gray-700);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-6);
}

.contact-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-2);
}

.nav-toggle span {
    width: 24px;
    height: 2px;
    background-color: var(--gray-700);
    transition: all var(--transition-fast);
}

/* Dropdown */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-2);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: var(--spacing-3) var(--spacing-4);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.dropdown-item:hover {
    background-color: var(--gray-50);
    color: var(--primary-color);
}

/* Hero Section */
.hero {
    position: relative;
    padding: calc(80px + var(--spacing-20)) 0 var(--spacing-20);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    overflow: hidden;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--spacing-6);
}

.highlight {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
    line-height: 1.7;
}

.hero-stats {
    display: flex;
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-10);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.hero-actions {
    display: flex;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

/* Hero Visual */
.hero-visual {
    position: relative;
    max-width: 500px;
    margin-left: auto;
}

.dashboard-preview {
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
}

.dashboard-header {
    background: var(--gray-100);
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--gray-200);
}

.dashboard-nav {
    display: flex;
    gap: var(--spacing-2);
}

.nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--gray-300);
}

.nav-dot:first-child { background: var(--error); }
.nav-dot:nth-child(2) { background: var(--warning); }
.nav-dot:nth-child(3) { background: var(--success); }

.dashboard-content {
    padding: var(--spacing-6);
}

.chart-container {
    display: flex;
    align-items: end;
    gap: var(--spacing-2);
    height: 120px;
    margin-bottom: var(--spacing-6);
}

.chart-bar {
    flex: 1;
    background: linear-gradient(to top, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-sm);
    animation: chartGrow 2s ease-out;
}

@keyframes chartGrow {
    from { height: 0; }
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-4);
}

.stat-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
}

.stat-icon {
    font-size: var(--font-size-xl);
}

.stat-value {
    display: block;
    font-weight: 600;
    color: var(--gray-900);
}

.stat-title {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: var(--white);
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    font-weight: 500;
    animation: float 3s ease-in-out infinite;
}

.card-1 {
    top: 20%;
    left: -10%;
    animation-delay: 0s;
}

.card-2 {
    top: 60%;
    right: -15%;
    animation-delay: 1s;
}

.card-3 {
    bottom: 20%;
    left: -5%;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Background Shapes */
.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    overflow: hidden;
}

.bg-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    opacity: 0.1;
    animation: bgFloat 20s ease-in-out infinite;
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: -150px;
    right: -150px;
    animation-delay: 0s;
}

.shape-2 {
    width: 200px;
    height: 200px;
    bottom: -100px;
    left: -100px;
    animation-delay: 5s;
}

.shape-3 {
    width: 150px;
    height: 150px;
    top: 50%;
    left: 10%;
    animation-delay: 10s;
}

@keyframes bgFloat {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
}

/* Section Styles */
section {
    padding: var(--spacing-20) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16);
}

.section-title {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-4);
}

.section-description {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* Features Section */
.features {
    background-color: var(--white);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-8);
}

.feature-card {
    padding: var(--spacing-8);
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-6);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.feature-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
}

.feature-description {
    color: var(--gray-600);
    line-height: 1.7;
}

/* Pricing Section */
.pricing {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
}

.pricing-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-12);
}

.toggle-label {
    font-weight: 500;
    color: var(--gray-700);
}

.discount {
    background: var(--accent-color);
    color: var(--white);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    margin-left: var(--spacing-2);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--gray-300);
    transition: var(--transition-normal);
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: var(--white);
    transition: var(--transition-normal);
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-8);
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-8);
    text-align: center;
    position: relative;
    transition: all var(--transition-normal);
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: var(--shadow-xl);
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.pricing-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-2) var(--spacing-6);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.pricing-header {
    margin-bottom: var(--spacing-8);
}

.pricing-title {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-2);
}

.pricing-subtitle {
    color: var(--gray-500);
    margin-bottom: var(--spacing-6);
}

.pricing-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--spacing-1);
}

.price-currency {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-700);
}

.price-amount {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--primary-color);
}

.price-period {
    font-size: var(--font-size-lg);
    color: var(--gray-500);
}

.pricing-features {
    margin-bottom: var(--spacing-8);
}

.feature-list {
    list-style: none;
    text-align: left;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3) 0;
    border-bottom: 1px solid var(--gray-100);
}

.feature-list li:last-child {
    border-bottom: none;
}

.feature-list i {
    width: 20px;
    text-align: center;
}

.fa-check {
    color: var(--success);
}

.fa-times {
    color: var(--gray-400);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-12);
        text-align: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .contact-info {
        display: none;
    }
    
    .hero {
        padding: calc(60px + var(--spacing-12)) 0 var(--spacing-12);
    }
    
    .hero-title {
        font-size: var(--font-size-4xl);
    }
    
    .hero-actions {
        justify-content: center;
    }
    
    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-4);
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .pricing-grid {
        grid-template-columns: 1fr;
    }
    
    .pricing-card.featured {
        transform: none;
    }
    
    .pricing-card.featured:hover {
        transform: translateY(-5px);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-3);
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .section-title {
        font-size: var(--font-size-3xl);
    }
    
    .btn-large {
        padding: var(--spacing-3) var(--spacing-6);
        font-size: var(--font-size-sm);
    }
    
    .hero-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .hero-actions .btn {
        width: 100%;
    }
}

/* Additional Styles for Interactive Elements */

/* Mobile Navigation Styles */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--white);
        border-top: 1px solid var(--gray-200);
        box-shadow: var(--shadow-lg);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
        z-index: 999;
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-list {
        flex-direction: column;
        padding: var(--spacing-4);
        gap: 0;
    }

    .nav-item {
        border-bottom: 1px solid var(--gray-100);
    }

    .nav-link {
        display: block;
        padding: var(--spacing-4);
    }

    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        background: var(--gray-50);
        margin-left: var(--spacing-4);
    }
}

/* Header Scroll Effect */
.header.scrolled {
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

/* Animation Classes */
.animate-in {
    animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: var(--spacing-4);
    right: var(--spacing-4);
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    z-index: 10000;
    min-width: 300px;
    animation: slideInRight 0.3s ease-out;
}

.notification-success {
    border-left: 4px solid var(--success);
}

.notification-error {
    border-left: 4px solid var(--error);
}

.notification-info {
    border-left: 4px solid var(--primary-color);
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-4);
}

.notification-close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    cursor: pointer;
    color: var(--gray-500);
    margin-left: var(--spacing-4);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: var(--spacing-4);
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-xl);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
}

.modal-title {
    margin: 0;
    font-size: var(--font-size-xl);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    cursor: pointer;
    color: var(--gray-500);
    padding: var(--spacing-2);
}

.modal-body {
    padding: var(--spacing-6);
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.9) translateY(-20px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* Form Styles */
.form-group {
    margin-bottom: var(--spacing-4);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: 500;
    color: var(--gray-700);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-3);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* WhatsApp Float Button */
.whatsapp-float {
    position: fixed;
    bottom: var(--spacing-6);
    right: var(--spacing-6);
    width: 60px;
    height: 60px;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: var(--font-size-2xl);
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    transition: all var(--transition-normal);
    animation: pulse 2s infinite;
}

.whatsapp-float:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

.whatsapp-tooltip {
    position: absolute;
    right: 70px;
    background: var(--gray-900);
    color: var(--white);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.whatsapp-float:hover .whatsapp-tooltip {
    opacity: 1;
    visibility: visible;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* Loading States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Utility Classes */
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }

.bg-success { background-color: var(--success); }
.bg-warning { background-color: var(--warning); }
.bg-error { background-color: var(--error); }

.hidden { display: none !important; }
.visible { display: block !important; }

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* References Section */
.references {
    background-color: var(--gray-50);
}

.references-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-16);
}

.reference-item {
    background: var(--white);
    padding: var(--spacing-6);
    border-radius: var(--radius-xl);
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.reference-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.reference-logo {
    margin-bottom: var(--spacing-4);
}

.logo-placeholder {
    width: 120px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 600;
    margin: 0 auto;
}

.reference-stats .stat {
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

/* Testimonials */
.testimonials {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-8);
}

.testimonial-card {
    background: var(--white);
    padding: var(--spacing-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    position: relative;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: var(--spacing-4);
    left: var(--spacing-6);
    font-size: 4rem;
    color: var(--primary-color);
    opacity: 0.3;
    font-family: serif;
}

.testimonial-content {
    margin-bottom: var(--spacing-6);
}

.testimonial-content p {
    font-style: italic;
    color: var(--gray-600);
    line-height: 1.7;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.author-name {
    display: block;
    font-weight: 600;
    color: var(--gray-900);
}

.author-title {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

/* FAQ Section */
.faq {
    background-color: var(--white);
}

.faq-list {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    border-bottom: 1px solid var(--gray-200);
    margin-bottom: var(--spacing-4);
}

.faq-question {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-6);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.faq-question:hover {
    background-color: var(--gray-50);
}

.faq-question h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    color: var(--gray-900);
}

.faq-question i {
    color: var(--primary-color);
    transition: transform var(--transition-normal);
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.faq-item.active .faq-answer {
    max-height: 200px;
    padding: 0 var(--spacing-6) var(--spacing-6);
}

.faq-answer p {
    color: var(--gray-600);
    line-height: 1.7;
    margin: 0;
}

/* CTA Section */
.cta {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    text-align: center;
}

.cta-title {
    font-size: var(--font-size-4xl);
    color: var(--white);
    margin-bottom: var(--spacing-6);
}

.cta-description {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto var(--spacing-10);
    line-height: 1.7;
}

.cta-actions {
    display: flex;
    gap: var(--spacing-4);
    justify-content: center;
    margin-bottom: var(--spacing-10);
    flex-wrap: wrap;
}

.cta .btn-outline {
    border-color: var(--white);
    color: var(--white);
}

.cta .btn-outline:hover {
    background-color: var(--white);
    color: var(--primary-color);
}

.cta-features {
    display: flex;
    gap: var(--spacing-8);
    justify-content: center;
    flex-wrap: wrap;
}

.cta-feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    opacity: 0.9;
}

.cta-feature i {
    color: var(--success);
}

/* Footer */
.footer {
    background-color: var(--gray-900);
    color: var(--gray-300);
    padding: var(--spacing-20) 0 0;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
    gap: var(--spacing-12);
    margin-bottom: var(--spacing-16);
}

.footer-brand {
    max-width: 300px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--white);
    text-decoration: none;
    margin-bottom: var(--spacing-4);
}

.footer-description {
    color: var(--gray-400);
    line-height: 1.7;
    margin-bottom: var(--spacing-6);
}

.social-links {
    display: flex;
    gap: var(--spacing-3);
}

.social-link {
    width: 40px;
    height: 40px;
    background-color: var(--gray-800);
    color: var(--gray-400);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all var(--transition-normal);
}

.social-link:hover {
    background-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.footer-title {
    color: var(--white);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-6);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-3);
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--white);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-4);
    color: var(--gray-400);
}

.contact-item i {
    color: var(--primary-color);
    width: 20px;
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding: var(--spacing-6) 0;
}

.footer-bottom-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-4);
}

.footer-bottom-links {
    display: flex;
    gap: var(--spacing-6);
}

.footer-bottom-links a {
    color: var(--gray-400);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: color var(--transition-fast);
}

.footer-bottom-links a:hover {
    color: var(--white);
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: var(--spacing-20);
    right: var(--spacing-6);
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all var(--transition-normal);
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
    .footer-content {
        grid-template-columns: 1fr 1fr 1fr;
        gap: var(--spacing-8);
    }

    .footer-brand {
        grid-column: 1 / -1;
        max-width: none;
        text-align: center;
        margin-bottom: var(--spacing-8);
    }

    .references-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .testimonials {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .cta-actions {
        flex-direction: column;
        align-items: center;
    }

    .cta-actions .btn {
        width: 100%;
        max-width: 300px;
    }

    .cta-features {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-4);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-8);
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .references-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .testimonials {
        display: flex;
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        gap: var(--spacing-4);
        padding-bottom: var(--spacing-4);
    }

    .testimonial-card {
        min-width: 300px;
        scroll-snap-align: start;
    }

    .whatsapp-float {
        bottom: var(--spacing-16);
    }

    .scroll-to-top {
        bottom: var(--spacing-32);
    }
}

@media (max-width: 480px) {
    .references-grid {
        grid-template-columns: 1fr;
    }

    .testimonial-card {
        min-width: 280px;
    }

    .cta-title {
        font-size: var(--font-size-3xl);
    }

    .faq-question h3 {
        font-size: var(--font-size-base);
    }

    .social-links {
        justify-content: center;
    }
}

/* Print Styles */
@media print {
    .header,
    .whatsapp-float,
    .scroll-to-top,
    .cta {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    .container {
        max-width: none;
        padding: 0;
    }

    section {
        padding: 20pt 0;
        page-break-inside: avoid;
    }

    .pricing-card,
    .feature-card,
    .testimonial-card {
        page-break-inside: avoid;
        box-shadow: none;
        border: 1pt solid #ccc;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --gray-100: #f0f0f0;
        --gray-200: #e0e0e0;
        --gray-300: #c0c0c0;
        --gray-400: #a0a0a0;
        --gray-500: #808080;
        --gray-600: #606060;
        --gray-700: #404040;
        --gray-800: #202020;
        --gray-900: #000000;
    }

    .btn-outline {
        border-width: 3px;
    }

    .feature-card,
    .pricing-card,
    .testimonial-card {
        border: 2px solid var(--gray-400);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .floating-card,
    .bg-shape {
        animation: none !important;
    }

    .scroll-to-top,
    .whatsapp-float {
        transition: none !important;
    }
}

/* Focus Styles for Accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn:focus {
    outline: 2px solid var(--white);
    outline-offset: 2px;
}

/* Skip Link for Screen Readers */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10001;
}

.skip-link:focus {
    top: 6px;
}

/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    padding: calc(80px + var(--spacing-12)) 0 var(--spacing-12);
    text-align: center;
}

.page-header-content {
    max-width: 800px;
    margin: 0 auto;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-6);
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.breadcrumb a {
    color: var(--white);
    text-decoration: none;
    transition: opacity var(--transition-fast);
}

.breadcrumb a:hover {
    opacity: 0.8;
}

.page-title {
    font-size: var(--font-size-4xl);
    color: var(--white);
    margin-bottom: var(--spacing-4);
}

.page-description {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    line-height: 1.7;
}

/* Package Comparison Table */
.package-comparison {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.comparison-table-wrapper {
    overflow-x: auto;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    background: var(--white);
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 800px;
}

.comparison-table th,
.comparison-table td {
    padding: var(--spacing-4);
    text-align: center;
    border-bottom: 1px solid var(--gray-200);
}

.feature-column {
    text-align: left !important;
    font-weight: 600;
    background-color: var(--gray-50);
    width: 30%;
    min-width: 200px;
}

.package-column {
    width: 23.33%;
    position: relative;
}

.package-column.featured {
    background-color: var(--primary-color);
    color: var(--white);
}

.package-column.featured .package-header {
    color: var(--white);
}

.package-header {
    padding: var(--spacing-6) var(--spacing-4);
    position: relative;
}

.popular-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--accent-color);
    color: var(--white);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.package-header h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
}

.package-price {
    margin-bottom: var(--spacing-6);
}

.package-price .price {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    display: block;
}

.package-price .period {
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.btn-sm {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-sm);
}

.category-row {
    background-color: var(--gray-100);
}

.category-title {
    font-weight: 600;
    color: var(--gray-900);
    text-align: left !important;
    padding: var(--spacing-4) var(--spacing-6);
}

.feature-name {
    font-weight: 500;
    color: var(--gray-700);
}

.feature-value {
    font-weight: 500;
}

.package-column.featured .feature-value {
    color: var(--white);
}

.package-column.featured .category-title {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--white);
}

/* Responsive Table */
@media (max-width: 1024px) {
    .comparison-table-wrapper {
        margin: 0 -var(--spacing-4);
    }

    .comparison-table th,
    .comparison-table td {
        padding: var(--spacing-3);
        font-size: var(--font-size-sm);
    }

    .package-header {
        padding: var(--spacing-4) var(--spacing-2);
    }

    .package-price .price {
        font-size: var(--font-size-2xl);
    }
}

@media (max-width: 768px) {
    .page-title {
        font-size: var(--font-size-3xl);
    }

    .comparison-table {
        min-width: 600px;
    }

    .comparison-table th,
    .comparison-table td {
        padding: var(--spacing-2);
        font-size: var(--font-size-xs);
    }

    .feature-column {
        min-width: 150px;
    }

    .package-header h3 {
        font-size: var(--font-size-lg);
    }

    .package-price .price {
        font-size: var(--font-size-xl);
    }

    .btn-sm {
        padding: var(--spacing-1) var(--spacing-3);
        font-size: var(--font-size-xs);
    }
}

/* Alternative Mobile Layout for Comparison */
@media (max-width: 480px) {
    .comparison-table-wrapper {
        display: none;
    }

    .mobile-comparison {
        display: block;
    }

    .mobile-package-card {
        background: var(--white);
        border-radius: var(--radius-xl);
        padding: var(--spacing-6);
        margin-bottom: var(--spacing-6);
        box-shadow: var(--shadow-md);
    }

    .mobile-package-card.featured {
        border: 2px solid var(--primary-color);
        position: relative;
    }

    .mobile-package-card.featured::before {
        content: 'En Popüler';
        position: absolute;
        top: -10px;
        left: 50%;
        transform: translateX(-50%);
        background: var(--primary-color);
        color: var(--white);
        padding: var(--spacing-1) var(--spacing-3);
        border-radius: var(--radius-lg);
        font-size: var(--font-size-xs);
        font-weight: 600;
    }

    .mobile-package-header {
        text-align: center;
        margin-bottom: var(--spacing-6);
        padding-bottom: var(--spacing-6);
        border-bottom: 1px solid var(--gray-200);
    }

    .mobile-package-features {
        list-style: none;
    }

    .mobile-package-features li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-3) 0;
        border-bottom: 1px solid var(--gray-100);
    }

    .mobile-package-features li:last-child {
        border-bottom: none;
    }
}

/* Hide mobile comparison by default */
.mobile-comparison {
    display: none;
}
