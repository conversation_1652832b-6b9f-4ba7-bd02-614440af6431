// DOM Elements
const navToggle = document.getElementById('nav-toggle');
const navMenu = document.getElementById('nav-menu');
const pricingToggle = document.getElementById('pricing-toggle');
const monthlyPrices = document.querySelectorAll('.monthly');
const yearlyPrices = document.querySelectorAll('.yearly');

// Campaign Countdown Timer
function initCountdown() {
    // Set campaign end date (30 days from now)
    const campaignEndDate = new Date();
    campaignEndDate.setDate(campaignEndDate.getDate() + 30);

    function updateCountdown() {
        const now = new Date().getTime();
        const distance = campaignEndDate.getTime() - now;

        if (distance < 0) {
            // Campaign ended
            document.getElementById('days').textContent = '0';
            document.getElementById('hours').textContent = '0';
            document.getElementById('minutes').textContent = '0';
            document.getElementById('seconds').textContent = '0';
            return;
        }

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        document.getElementById('days').textContent = days;
        document.getElementById('hours').textContent = hours;
        document.getElementById('minutes').textContent = minutes;
        document.getElementById('seconds').textContent = seconds;
    }

    // Update countdown every second
    updateCountdown();
    setInterval(updateCountdown, 1000);
}

// Initialize countdown on page load
document.addEventListener('DOMContentLoaded', initCountdown);

// Mobile Navigation Toggle
if (navToggle && navMenu) {
    navToggle.addEventListener('click', () => {
        navMenu.classList.toggle('active');
        navToggle.classList.toggle('active');
    });
}

// Pricing Toggle (Monthly/Yearly)
if (pricingToggle) {
    pricingToggle.addEventListener('change', () => {
        const isYearly = pricingToggle.checked;
        
        monthlyPrices.forEach(price => {
            price.style.display = isYearly ? 'none' : 'inline';
        });
        
        yearlyPrices.forEach(price => {
            price.style.display = isYearly ? 'inline' : 'none';
        });
    });
}

// Smooth Scrolling for Navigation Links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            const headerHeight = document.querySelector('.header').offsetHeight;
            const targetPosition = target.offsetTop - headerHeight;
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    });
});

// Header Scroll Effect
window.addEventListener('scroll', () => {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.classList.add('scrolled');
    } else {
        header.classList.remove('scrolled');
    }
});

// Intersection Observer for Animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
        }
    });
}, observerOptions);

// Observe elements for animation
document.querySelectorAll('.feature-card, .pricing-card').forEach(el => {
    observer.observe(el);
});

// Counter Animation
function animateCounter(element, target, duration = 2000) {
    let start = 0;
    const increment = target / (duration / 16);
    
    const timer = setInterval(() => {
        start += increment;
        if (start >= target) {
            element.textContent = target;
            clearInterval(timer);
        } else {
            element.textContent = Math.floor(start);
        }
    }, 16);
}

// Animate counters when they come into view
const counterObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const counter = entry.target;
            const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
            animateCounter(counter, target);
            counterObserver.unobserve(counter);
        }
    });
}, { threshold: 0.5 });

document.querySelectorAll('.stat-number').forEach(counter => {
    counterObserver.observe(counter);
});

// Form Handling
function handleFormSubmit(formId, successMessage) {
    const form = document.getElementById(formId);
    if (form) {
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Gönderiliyor...';
            submitBtn.disabled = true;
            
            // Simulate form submission
            setTimeout(() => {
                showNotification(successMessage, 'success');
                form.reset();
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }, 1500);
        });
    }
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.remove();
    }, 5000);
    
    // Manual close
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.remove();
    });
}

// Modal System
function createModal(content, options = {}) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">${options.title || 'Modal'}</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    document.body.style.overflow = 'hidden';
    
    // Close modal events
    const closeModal = () => {
        modal.remove();
        document.body.style.overflow = '';
    };
    
    modal.querySelector('.modal-close').addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });
    
    // ESC key to close
    const escHandler = (e) => {
        if (e.key === 'Escape') {
            closeModal();
            document.removeEventListener('keydown', escHandler);
        }
    };
    document.addEventListener('keydown', escHandler);
    
    return modal;
}

// Demo Modal
function showDemoModal() {
    const demoContent = `
        <div class="demo-form">
            <p>Demo talebiniz için lütfen bilgilerinizi doldurun:</p>
            <form id="demo-form">
                <div class="form-group">
                    <label for="demo-name">Ad Soyad</label>
                    <input type="text" id="demo-name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="demo-email">E-posta</label>
                    <input type="email" id="demo-email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="demo-phone">Telefon</label>
                    <input type="tel" id="demo-phone" name="phone" required>
                </div>
                <div class="form-group">
                    <label for="demo-company">Şirket Adı</label>
                    <input type="text" id="demo-company" name="company">
                </div>
                <button type="submit" class="btn btn-primary btn-full">Demo Talep Et</button>
            </form>
        </div>
    `;
    
    const modal = createModal(demoContent, { title: 'Ücretsiz Demo Talebi' });
    handleFormSubmit('demo-form', 'Demo talebiniz başarıyla gönderildi! En kısa sürede sizinle iletişime geçeceğiz.');
}

// Contact Modal
function showContactModal() {
    const contactContent = `
        <div class="contact-form">
            <p>Size nasıl yardımcı olabiliriz?</p>
            <form id="contact-form">
                <div class="form-group">
                    <label for="contact-name">Ad Soyad</label>
                    <input type="text" id="contact-name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="contact-email">E-posta</label>
                    <input type="email" id="contact-email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="contact-subject">Konu</label>
                    <select id="contact-subject" name="subject" required>
                        <option value="">Konu Seçin</option>
                        <option value="demo">Demo Talebi</option>
                        <option value="pricing">Fiyatlandırma</option>
                        <option value="technical">Teknik Destek</option>
                        <option value="other">Diğer</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="contact-message">Mesaj</label>
                    <textarea id="contact-message" name="message" rows="4" required></textarea>
                </div>
                <button type="submit" class="btn btn-primary btn-full">Mesaj Gönder</button>
            </form>
        </div>
    `;
    
    const modal = createModal(contactContent, { title: 'İletişim' });
    handleFormSubmit('contact-form', 'Mesajınız başarıyla gönderildi! En kısa sürede size dönüş yapacağız.');
}

// Event Listeners for Buttons
document.addEventListener('DOMContentLoaded', () => {
    // Demo buttons
    document.querySelectorAll('.btn:contains("Demo"), .btn:contains("Deneyin")').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            showDemoModal();
        });
    });
    
    // Contact buttons
    document.querySelectorAll('.btn:contains("İletişim")').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            showContactModal();
        });
    });
    
    // Package selection buttons
    document.querySelectorAll('.pricing-card .btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const packageName = btn.closest('.pricing-card').querySelector('.pricing-title').textContent;
            
            if (btn.textContent.includes('İletişim')) {
                showContactModal();
            } else {
                showNotification(`${packageName} paketi seçildi! Satış temsilcimiz sizinle iletişime geçecek.`, 'success');
            }
        });
    });
});

// Utility function to check if text contains substring
HTMLElement.prototype.contains = function(text) {
    return this.textContent.toLowerCase().includes(text.toLowerCase());
};

// WhatsApp Integration
function openWhatsApp() {
    const phoneNumber = '905551234567'; // Replace with actual number
    const message = 'Merhaba, TechCommerce hakkında bilgi almak istiyorum.';
    const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(url, '_blank');
}

// Add WhatsApp floating button
function addWhatsAppButton() {
    const whatsappBtn = document.createElement('div');
    whatsappBtn.className = 'whatsapp-float';
    whatsappBtn.innerHTML = `
        <i class="fab fa-whatsapp"></i>
        <span class="whatsapp-tooltip">WhatsApp ile iletişim</span>
    `;
    whatsappBtn.addEventListener('click', openWhatsApp);
    document.body.appendChild(whatsappBtn);
}

// Initialize WhatsApp button
document.addEventListener('DOMContentLoaded', addWhatsAppButton);

// Performance optimization: Lazy load images
function lazyLoadImages() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// Initialize lazy loading
document.addEventListener('DOMContentLoaded', lazyLoadImages);

// Analytics tracking (placeholder)
function trackEvent(eventName, eventData = {}) {
    // Google Analytics or other tracking service integration
    console.log('Event tracked:', eventName, eventData);
    
    // Example: gtag('event', eventName, eventData);
}

// Track button clicks
document.addEventListener('click', (e) => {
    if (e.target.matches('.btn')) {
        trackEvent('button_click', {
            button_text: e.target.textContent.trim(),
            button_location: e.target.closest('section')?.id || 'unknown'
        });
    }
});

// Page load performance
window.addEventListener('load', () => {
    const loadTime = performance.now();
    trackEvent('page_load', {
        load_time: Math.round(loadTime)
    });
});

// FAQ Accordion
document.querySelectorAll('.faq-question').forEach(question => {
    question.addEventListener('click', () => {
        const faqItem = question.parentElement;
        const isActive = faqItem.classList.contains('active');

        // Close all FAQ items
        document.querySelectorAll('.faq-item').forEach(item => {
            item.classList.remove('active');
        });

        // Open clicked item if it wasn't active
        if (!isActive) {
            faqItem.classList.add('active');
        }
    });
});

// Enhanced Button Click Handlers
document.addEventListener('DOMContentLoaded', () => {
    // Demo buttons - more specific targeting
    const demoButtons = document.querySelectorAll('button, a');
    demoButtons.forEach(btn => {
        const text = btn.textContent.toLowerCase();
        if (text.includes('demo') || text.includes('deneyin') || text.includes('başla')) {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                showDemoModal();
            });
        }
    });

    // Contact buttons
    const contactButtons = document.querySelectorAll('button, a');
    contactButtons.forEach(btn => {
        const text = btn.textContent.toLowerCase();
        if (text.includes('iletişim') || text.includes('geç')) {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                showContactModal();
            });
        }
    });

    // Phone buttons
    const phoneButtons = document.querySelectorAll('button, a');
    phoneButtons.forEach(btn => {
        const text = btn.textContent;
        if (text.includes('0850') || text.includes('ara')) {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                window.location.href = 'tel:+908501234567';
            });
        }
    });

    // Package selection buttons
    document.querySelectorAll('.pricing-card .btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const packageName = btn.closest('.pricing-card').querySelector('.pricing-title').textContent;

            if (btn.textContent.includes('İletişim') || btn.textContent.includes('Geç')) {
                showContactModal();
            } else {
                showPackageModal(packageName);
            }
        });
    });
});

// Package Selection Modal
function showPackageModal(packageName) {
    const packageContent = `
        <div class="package-form">
            <p><strong>${packageName}</strong> paketini seçtiniz. Hemen başlamak için bilgilerinizi doldurun:</p>
            <form id="package-form">
                <div class="form-group">
                    <label for="package-name">Ad Soyad</label>
                    <input type="text" id="package-name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="package-email">E-posta</label>
                    <input type="email" id="package-email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="package-phone">Telefon</label>
                    <input type="tel" id="package-phone" name="phone" required>
                </div>
                <div class="form-group">
                    <label for="package-company">Şirket Adı</label>
                    <input type="text" id="package-company" name="company">
                </div>
                <div class="form-group">
                    <label for="package-website">Mevcut Web Siteniz (varsa)</label>
                    <input type="url" id="package-website" name="website">
                </div>
                <button type="submit" class="btn btn-primary btn-full">
                    ${packageName} Paketini Başlat
                </button>
            </form>
        </div>
    `;

    const modal = createModal(packageContent, { title: `${packageName} Paketi` });
    handleFormSubmit('package-form', `${packageName} paketi için başvurunuz alındı! Satış temsilcimiz en kısa sürede sizinle iletişime geçecek.`);
}

// Scroll to Top Button
function addScrollToTopButton() {
    const scrollBtn = document.createElement('div');
    scrollBtn.className = 'scroll-to-top';
    scrollBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
    scrollBtn.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    document.body.appendChild(scrollBtn);

    // Show/hide based on scroll position
    window.addEventListener('scroll', () => {
        if (window.scrollY > 500) {
            scrollBtn.classList.add('visible');
        } else {
            scrollBtn.classList.remove('visible');
        }
    });
}

// Initialize scroll to top button
document.addEventListener('DOMContentLoaded', addScrollToTopButton);

// Enhanced Smooth Scrolling with offset
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const targetId = this.getAttribute('href');
        const target = document.querySelector(targetId);

        if (target) {
            const headerHeight = document.querySelector('.header').offsetHeight;
            const targetPosition = target.offsetTop - headerHeight - 20;

            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });

            // Update URL without jumping
            history.pushState(null, null, targetId);
        }
    });
});

// Testimonial Slider (if needed)
function initTestimonialSlider() {
    const testimonials = document.querySelector('.testimonials');
    if (testimonials && window.innerWidth <= 768) {
        // Convert to slider on mobile
        testimonials.style.display = 'flex';
        testimonials.style.overflowX = 'auto';
        testimonials.style.scrollSnapType = 'x mandatory';

        testimonials.querySelectorAll('.testimonial-card').forEach(card => {
            card.style.minWidth = '300px';
            card.style.scrollSnapAlign = 'start';
        });
    }
}

// Initialize testimonial slider
window.addEventListener('resize', initTestimonialSlider);
document.addEventListener('DOMContentLoaded', initTestimonialSlider);

// Video Testimonial Modal
function showVideoModal(videoId) {
    const videoContent = `
        <div class="video-modal-content">
            <div class="video-placeholder">
                <div class="video-info">
                    <h3>Müşteri Referans Videosu</h3>
                    <p>Bu bölümde ${videoId} firmasının TechCommerce deneyimi hakkında video bulunacaktır.</p>
                    <div class="video-features">
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            <span>Gerçek müşteri deneyimi</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            <span>Satış artışı hikayeleri</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-check"></i>
                            <span>Platform entegrasyonu deneyimleri</span>
                        </div>
                    </div>
                    <button class="btn btn-primary">
                        <i class="fas fa-phone"></i>
                        Bizimle İletişime Geçin
                    </button>
                </div>
            </div>
        </div>
    `;

    const modal = createModal(videoContent, { title: 'Müşteri Referansı' });
}

// Video testimonial click handlers
document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.video-testimonial').forEach(video => {
        video.addEventListener('click', () => {
            const videoId = video.dataset.video;
            showVideoModal(videoId);
        });
    });
});

// AI Chat Animation
function initAIChatAnimation() {
    const typingMessage = document.querySelector('.chat-message.typing');
    const aiResponse = document.querySelector('.chat-message.ai:not(.typing)');

    if (typingMessage && aiResponse) {
        // Hide AI response initially
        aiResponse.style.display = 'none';

        // Show typing indicator for 3 seconds, then show response
        setTimeout(() => {
            typingMessage.style.display = 'none';
            aiResponse.style.display = 'flex';
            aiResponse.style.animation = 'slideInUp 0.5s ease-out';
        }, 3000);
    }
}

// Initialize AI chat animation when section comes into view
const aiObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            initAIChatAnimation();
            aiObserver.unobserve(entry.target);
        }
    });
}, { threshold: 0.5 });

document.addEventListener('DOMContentLoaded', () => {
    const aiSection = document.querySelector('.ai-section');
    if (aiSection) {
        aiObserver.observe(aiSection);
    }
});

// Enhanced scroll animations
function initScrollAnimations() {
    const animateElements = document.querySelectorAll('.integration-category, .ai-demo, .video-testimonial');

    const scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animation = 'slideInUp 0.6s ease-out forwards';
                scrollObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        scrollObserver.observe(el);
    });
}

// Initialize scroll animations
document.addEventListener('DOMContentLoaded', initScrollAnimations);
